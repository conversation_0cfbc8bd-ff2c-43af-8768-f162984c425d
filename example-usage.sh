#!/bin/bash

# New-API 管理脚本使用示例
# 这个脚本演示了如何使用 manage-new-api.sh 进行各种操作

set -e

echo "=== New-API 管理脚本使用示例 ==="

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "\n${BLUE}1. 查看帮助信息${NC}"
./manage-new-api.sh help

echo -e "\n${BLUE}2. 查看版本信息${NC}"
./manage-new-api.sh version

echo -e "\n${BLUE}3. 查看服务状态${NC}"
./manage-new-api.sh status

echo -e "\n${BLUE}4. 列出现有备份${NC}"
./manage-new-api.sh list-backups

echo -e "\n${BLUE}5. 创建数据库备份${NC}"
echo -e "${YELLOW}正在创建备份...${NC}"
./manage-new-api.sh backup

echo -e "\n${BLUE}6. 再次列出备份（应该能看到新创建的备份）${NC}"
./manage-new-api.sh list-backups

echo -e "\n${BLUE}7. 检查是否有可用更新${NC}"
./manage-new-api.sh pull

echo -e "\n${GREEN}=== 示例完成 ===${NC}"
echo -e "${GREEN}您现在可以使用以下命令管理 New-API:${NC}"
echo "  ./manage-new-api.sh start     # 启动服务"
echo "  ./manage-new-api.sh stop      # 停止服务"
echo "  ./manage-new-api.sh restart   # 重启服务"
echo "  ./manage-new-api.sh backup    # 备份数据库"
echo "  ./manage-new-api.sh update    # 更新到最新版本"
echo "  ./manage-new-api.sh health    # 健康检查"
echo "  ./manage-new-api.sh cleanup   # 清理Docker资源"
