#!/bin/bash

# New-API 服务管理脚本
# 负责启动、停止、重启、状态查看等服务管理功能

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
PROJECT_NAME="new-api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 检查Docker Compose文件
check_compose_file() {
    if [ ! -f "${COMPOSE_FILE}" ]; then
        log_error "Docker Compose 文件不存在: ${COMPOSE_FILE}"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动 ${PROJECT_NAME} 服务..."
    
    # 检查是否已经运行
    if docker-compose -f "${COMPOSE_FILE}" ps | grep -q "Up"; then
        log_warn "服务已经在运行中"
        return 0
    fi
    
    # 启动服务
    docker-compose -f "${COMPOSE_FILE}" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if check_service_health; then
        log_info "服务启动成功！"
        show_service_info
    else
        log_error "服务启动失败，请检查日志"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 ${PROJECT_NAME} 服务..."
    
    docker-compose -f "${COMPOSE_FILE}" down
    
    log_info "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启 ${PROJECT_NAME} 服务..."
    
    stop_service
    sleep 5
    start_service
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}=== ${PROJECT_NAME} 服务状态 ===${NC}"
    
    # Docker Compose 状态
    echo -e "\n${GREEN}Docker Compose 状态:${NC}"
    docker-compose -f "${COMPOSE_FILE}" ps
    
    # 容器详细信息
    echo -e "\n${GREEN}容器详细信息:${NC}"
    local containers=("new-api" "mysql" "redis")
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q "$container"; then
            echo -e "${GREEN}✓${NC} $container: 运行中"
        else
            echo -e "${RED}✗${NC} $container: 未运行"
        fi
    done
    
    # 健康检查
    echo -e "\n${GREEN}健康检查:${NC}"
    check_service_health
    
    # 资源使用情况
    echo -e "\n${GREEN}资源使用情况:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $(docker ps --format "{{.Names}}" | grep -E "(new-api|mysql|redis)")
}

# 检查服务健康状态
check_service_health() {
    local health_url="http://localhost:3000/api/status"
    
    if curl -s "$health_url" | grep -q '"success":true'; then
        echo -e "${GREEN}✓${NC} API 服务健康"
        return 0
    else
        echo -e "${RED}✗${NC} API 服务不健康"
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    echo -e "\n${BLUE}=== 服务信息 ===${NC}"
    echo -e "${GREEN}Web 界面:${NC} http://localhost:3000"
    echo -e "${GREEN}API 端点:${NC} http://localhost:3000/api"
    echo -e "${GREEN}健康检查:${NC} http://localhost:3000/api/status"
}

# 查看日志
show_logs() {
    local service=${1:-""}
    local lines=${2:-100}
    
    if [ -n "$service" ]; then
        log_info "显示 $service 服务的最近 $lines 行日志..."
        docker-compose -f "${COMPOSE_FILE}" logs --tail="$lines" -f "$service"
    else
        log_info "显示所有服务的最近 $lines 行日志..."
        docker-compose -f "${COMPOSE_FILE}" logs --tail="$lines" -f
    fi
}

# 主函数
main() {
    check_compose_file
    
    local command=${1:-"status"}
    
    case $command in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            shift
            show_logs "$@"
            ;;
        health)
            check_service_health
            ;;
        info)
            show_service_info
            ;;
        *)
            log_error "未知命令: $command"
            echo "可用命令: start, stop, restart, status, logs, health, info"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
