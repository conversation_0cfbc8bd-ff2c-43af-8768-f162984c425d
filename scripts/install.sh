#!/bin/bash

# New-API 安装和初始化脚本
# 负责设置脚本权限、创建必要目录、初始化配置等

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_NAME="new-api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local deps=("docker" "docker-compose" "curl" "nc" "crontab")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        echo -e "\n${YELLOW}安装建议:${NC}"
        echo "Ubuntu/Debian: sudo apt-get install docker.io docker-compose curl netcat-openbsd cron"
        echo "CentOS/RHEL: sudo yum install docker docker-compose curl nc cronie"
        exit 1
    fi
    
    log_info "✓ 所有依赖已满足"
}

# 设置脚本权限
set_permissions() {
    log_info "设置脚本执行权限..."
    
    # 主脚本
    chmod +x "${SCRIPT_DIR}/manage-new-api.sh"
    
    # 子脚本
    local scripts=(
        "service.sh"
        "update.sh"
        "backup.sh"
        "maintenance.sh"
        "install.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "${SCRIPT_DIR}/scripts/$script" ]; then
            chmod +x "${SCRIPT_DIR}/scripts/$script"
            log_info "✓ 设置权限: scripts/$script"
        fi
    done
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    local dirs=(
        "backup"
        "logs"
        "data"
        "scripts"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "${SCRIPT_DIR}/$dir" ]; then
            mkdir -p "${SCRIPT_DIR}/$dir"
            log_info "✓ 创建目录: $dir"
        fi
    done
}

# 初始化配置文件
init_config() {
    log_info "初始化配置文件..."
    
    # 创建环境配置文件
    local env_file="${SCRIPT_DIR}/.env"
    if [ ! -f "$env_file" ]; then
        cat > "$env_file" << EOF
# New-API 环境配置文件
# 此文件包含敏感信息，请勿提交到版本控制系统

# 数据库配置
DB_PASSWORD=123456
DB_NAME=new-api
DB_USER=root

# 时区设置
TZ=Asia/Shanghai

# 日志配置
ERROR_LOG_ENABLED=true

# 备份配置
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=false
AUTO_BACKUP_SCHEDULE="0 2 * * *"

# 更新配置
AUTO_UPDATE_ENABLED=false
UPDATE_CHECK_INTERVAL=24h

# 监控配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
EOF
        log_info "✓ 创建环境配置文件: .env"
    fi
    
    # 创建日志配置
    local logrotate_config="${SCRIPT_DIR}/scripts/logrotate.conf"
    if [ ! -f "$logrotate_config" ]; then
        cat > "$logrotate_config" << EOF
# New-API 日志轮转配置
${SCRIPT_DIR}/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        # 重启服务以重新打开日志文件（如果需要）
        # docker-compose -f ${SCRIPT_DIR}/docker-compose.yml restart new-api
    endscript
}
EOF
        log_info "✓ 创建日志轮转配置: scripts/logrotate.conf"
    fi
}

# 检查Docker服务
check_docker() {
    log_info "检查Docker服务..."
    
    if ! systemctl is-active --quiet docker; then
        log_warn "Docker服务未运行，尝试启动..."
        if sudo systemctl start docker; then
            log_info "✓ Docker服务已启动"
        else
            log_error "Docker服务启动失败"
            exit 1
        fi
    fi
    
    # 检查当前用户是否在docker组中
    if ! groups | grep -q docker; then
        log_warn "当前用户不在docker组中"
        echo -e "${YELLOW}建议执行以下命令将用户添加到docker组:${NC}"
        echo "sudo usermod -aG docker \$USER"
        echo "然后重新登录或执行: newgrp docker"
    fi
    
    log_info "✓ Docker服务检查完成"
}

# 创建系统服务文件（可选）
create_systemd_service() {
    local create_service=${1:-false}
    
    if [ "$create_service" != "true" ]; then
        return 0
    fi
    
    log_info "创建systemd服务文件..."
    
    local service_file="/etc/systemd/system/new-api.service"
    
    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=New-API Service
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=${SCRIPT_DIR}
ExecStart=${SCRIPT_DIR}/manage-new-api.sh start
ExecStop=${SCRIPT_DIR}/manage-new-api.sh stop
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    log_info "✓ 系统服务文件已创建: $service_file"
    log_info "使用以下命令管理服务:"
    echo "  启用开机自启: sudo systemctl enable new-api"
    echo "  启动服务: sudo systemctl start new-api"
    echo "  查看状态: sudo systemctl status new-api"
}

# 显示安装完成信息
show_completion_info() {
    echo -e "\n${BLUE}=== 安装完成 ===${NC}"
    echo -e "${GREEN}New-API 管理脚本已成功安装！${NC}"
    
    echo -e "\n${GREEN}可用命令:${NC}"
    echo "  ./manage-new-api.sh start     # 启动服务"
    echo "  ./manage-new-api.sh status    # 查看状态"
    echo "  ./manage-new-api.sh backup    # 备份数据"
    echo "  ./manage-new-api.sh update    # 更新服务"
    echo "  ./manage-new-api.sh help      # 查看帮助"
    
    echo -e "\n${GREEN}配置文件:${NC}"
    echo "  .env                          # 环境配置"
    echo "  docker-compose.yml            # Docker配置"
    echo "  scripts/logrotate.conf        # 日志轮转配置"
    
    echo -e "\n${GREEN}目录结构:${NC}"
    echo "  backup/                       # 备份文件"
    echo "  logs/                         # 日志文件"
    echo "  data/                         # 应用数据"
    echo "  scripts/                      # 管理脚本"
    
    echo -e "\n${YELLOW}下一步:${NC}"
    echo "1. 检查并修改 docker-compose.yml 中的配置"
    echo "2. 运行 ./manage-new-api.sh start 启动服务"
    echo "3. 访问 http://localhost:3000 查看Web界面"
}

# 主函数
main() {
    local create_service=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --systemd)
                create_service=true
                shift
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --systemd    创建systemd服务文件"
                echo "  --help       显示此帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "开始安装 ${PROJECT_NAME} 管理脚本..."
    
    check_dependencies
    check_docker
    create_directories
    set_permissions
    init_config
    create_systemd_service "$create_service"
    
    show_completion_info
    
    log_info "安装完成！"
}

# 运行主函数
main "$@"
