#!/bin/bash

# New-API 维护管理脚本
# 负责清理、健康检查、重置等维护功能

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
PROJECT_NAME="new-api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 清理Docker资源
cleanup_docker() {
    log_info "开始清理Docker资源..."
    
    echo -e "\n${BLUE}=== 清理前资源使用情况 ===${NC}"
    docker system df
    
    # 清理未使用的容器
    log_info "清理停止的容器..."
    docker container prune -f
    
    # 清理未使用的镜像
    log_info "清理未使用的镜像..."
    docker image prune -f
    
    # 清理未使用的网络
    log_info "清理未使用的网络..."
    docker network prune -f
    
    # 清理未使用的卷（谨慎操作）
    read -p "是否清理未使用的卷？这可能会删除重要数据 (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_warn "清理未使用的卷..."
        docker volume prune -f
    fi
    
    echo -e "\n${BLUE}=== 清理后资源使用情况 ===${NC}"
    docker system df
    
    log_info "✓ Docker资源清理完成"
}

# 健康检查
health_check() {
    echo -e "${BLUE}=== ${PROJECT_NAME} 健康检查 ===${NC}"
    
    local overall_health=true
    
    # 检查Docker服务
    echo -e "\n${GREEN}1. Docker 服务检查:${NC}"
    if systemctl is-active --quiet docker; then
        echo -e "${GREEN}✓${NC} Docker 服务运行正常"
    else
        echo -e "${RED}✗${NC} Docker 服务未运行"
        overall_health=false
    fi
    
    # 检查容器状态
    echo -e "\n${GREEN}2. 容器状态检查:${NC}"
    local containers=("new-api" "mysql" "redis")
    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
            local status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            if [ "$status" = "healthy" ] || [ "$status" = "no-healthcheck" ]; then
                echo -e "${GREEN}✓${NC} $container: 运行正常"
            else
                echo -e "${YELLOW}⚠${NC} $container: 状态异常 ($status)"
                overall_health=false
            fi
        else
            echo -e "${RED}✗${NC} $container: 未运行"
            overall_health=false
        fi
    done
    
    # 检查端口连通性
    echo -e "\n${GREEN}3. 端口连通性检查:${NC}"
    local ports=("3000")
    for port in "${ports[@]}"; do
        if nc -z localhost "$port" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} 端口 $port: 可访问"
        else
            echo -e "${RED}✗${NC} 端口 $port: 不可访问"
            overall_health=false
        fi
    done
    
    # 检查API健康状态
    echo -e "\n${GREEN}4. API 健康状态检查:${NC}"
    if curl -s -f http://localhost:3000/api/status | grep -q '"success":true'; then
        echo -e "${GREEN}✓${NC} API 响应正常"
    else
        echo -e "${RED}✗${NC} API 响应异常"
        overall_health=false
    fi
    
    # 检查数据库连接
    echo -e "\n${GREEN}5. 数据库连接检查:${NC}"
    if docker exec mysql mysql -uroot -p123456 -e "SELECT 1;" new-api >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} 数据库连接正常"
    else
        echo -e "${RED}✗${NC} 数据库连接失败"
        overall_health=false
    fi
    
    # 检查Redis连接
    echo -e "\n${GREEN}6. Redis 连接检查:${NC}"
    if docker exec redis redis-cli ping | grep -q "PONG"; then
        echo -e "${GREEN}✓${NC} Redis 连接正常"
    else
        echo -e "${RED}✗${NC} Redis 连接失败"
        overall_health=false
    fi
    
    # 检查磁盘空间
    echo -e "\n${GREEN}7. 磁盘空间检查:${NC}"
    local disk_usage=$(df "${SCRIPT_DIR}" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        echo -e "${GREEN}✓${NC} 磁盘空间充足 (${disk_usage}% 已使用)"
    elif [ "$disk_usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠${NC} 磁盘空间不足 (${disk_usage}% 已使用)"
        overall_health=false
    else
        echo -e "${RED}✗${NC} 磁盘空间严重不足 (${disk_usage}% 已使用)"
        overall_health=false
    fi
    
    # 检查日志文件大小
    echo -e "\n${GREEN}8. 日志文件检查:${NC}"
    if [ -d "${SCRIPT_DIR}/logs" ]; then
        local log_size=$(du -sh "${SCRIPT_DIR}/logs" | cut -f1)
        echo -e "${GREEN}✓${NC} 日志目录大小: $log_size"
        
        # 检查是否有过大的日志文件
        find "${SCRIPT_DIR}/logs" -name "*.log" -size +100M | while read -r large_log; do
            echo -e "${YELLOW}⚠${NC} 发现大日志文件: $(basename "$large_log") ($(du -h "$large_log" | cut -f1))"
        done
    fi
    
    # 总结
    echo -e "\n${BLUE}=== 健康检查总结 ===${NC}"
    if [ "$overall_health" = true ]; then
        echo -e "${GREEN}✓ 系统整体健康状况良好${NC}"
        return 0
    else
        echo -e "${RED}✗ 发现健康问题，请检查上述报告${NC}"
        return 1
    fi
}

# 重置服务（危险操作）
reset_service() {
    log_warn "这是一个危险操作，将会删除所有数据和配置！"
    echo -e "${RED}此操作将会：${NC}"
    echo "1. 停止所有服务"
    echo "2. 删除所有容器"
    echo "3. 删除数据库数据"
    echo "4. 删除应用数据"
    echo "5. 删除日志文件"

    echo -e "\n${YELLOW}请确保您已经备份了重要数据！${NC}"

    read -p "确认要重置服务吗？输入 'RESET' 确认: " -r
    if [ "$REPLY" != "RESET" ]; then
        log_info "操作已取消"
        exit 0
    fi

    log_warn "开始重置服务..."

    # 创建最后的备份
    log_info "创建最后的备份..."
    if [ -f "${SCRIPT_DIR}/scripts/backup.sh" ]; then
        bash "${SCRIPT_DIR}/scripts/backup.sh" backup --auto || log_warn "备份失败，继续重置..."
    fi

    # 停止服务
    log_info "停止所有服务..."
    docker-compose -f "${COMPOSE_FILE}" down -v

    # 删除相关容器
    log_info "删除相关容器..."
    docker rm -f new-api mysql redis 2>/dev/null || true

    # 删除相关镜像（可选）
    read -p "是否删除相关镜像？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除相关镜像..."
        docker rmi calciumion/new-api:latest mysql:8.2 redis:latest 2>/dev/null || true
    fi

    # 删除数据目录
    if [ -d "${SCRIPT_DIR}/data" ]; then
        log_info "删除数据目录..."
        rm -rf "${SCRIPT_DIR}/data"
    fi

    # 清理日志文件
    if [ -d "${SCRIPT_DIR}/logs" ]; then
        log_info "清理日志文件..."
        rm -rf "${SCRIPT_DIR}/logs"/*
    fi

    # 删除Docker卷
    log_info "删除Docker卷..."
    docker volume rm new-api_mysql_data 2>/dev/null || true

    log_info "✓ 服务重置完成"
    log_info "您现在可以重新启动服务: ./manage-new-api.sh start"
}

# 日志轮转
rotate_logs() {
    local log_dir="${SCRIPT_DIR}/logs"
    local max_size=${1:-100}  # 默认100MB

    log_info "开始日志轮转..."

    if [ ! -d "$log_dir" ]; then
        log_warn "日志目录不存在: $log_dir"
        return 0
    fi

    # 查找大于指定大小的日志文件
    find "$log_dir" -name "*.log" -size +${max_size}M | while read -r log_file; do
        local file_size=$(du -m "$log_file" | cut -f1)
        log_info "轮转日志文件: $(basename "$log_file") (${file_size}MB)"

        # 创建轮转文件
        local timestamp=$(date +%Y%m%d_%H%M%S)
        local rotated_file="${log_file}.${timestamp}"

        mv "$log_file" "$rotated_file"
        gzip "$rotated_file"

        # 创建新的空日志文件
        touch "$log_file"

        log_info "✓ 日志文件已轮转: $(basename "$rotated_file").gz"
    done

    # 清理旧的轮转日志（保留30天）
    find "$log_dir" -name "*.log.*.gz" -mtime +30 -delete

    log_info "✓ 日志轮转完成"
}

# 性能监控
monitor_performance() {
    echo -e "${BLUE}=== ${PROJECT_NAME} 性能监控 ===${NC}"

    # 容器资源使用情况
    echo -e "\n${GREEN}容器资源使用情况:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" $(docker ps --format "{{.Names}}" | grep -E "(new-api|mysql|redis)")

    # 系统负载
    echo -e "\n${GREEN}系统负载:${NC}"
    uptime

    # 内存使用情况
    echo -e "\n${GREEN}内存使用情况:${NC}"
    free -h

    # 磁盘使用情况
    echo -e "\n${GREEN}磁盘使用情况:${NC}"
    df -h "${SCRIPT_DIR}"

    # 网络连接
    echo -e "\n${GREEN}网络连接:${NC}"
    netstat -tlnp | grep :3000 || echo "端口3000未监听"

    # Docker系统信息
    echo -e "\n${GREEN}Docker系统信息:${NC}"
    docker system df
}

# 主函数
main() {
    local command=${1:-"health"}

    case $command in
        cleanup)
            cleanup_docker
            ;;
        health)
            health_check
            ;;
        reset)
            reset_service
            ;;
        rotate-logs)
            shift
            rotate_logs "$@"
            ;;
        monitor)
            monitor_performance
            ;;
        *)
            log_error "未知命令: $command"
            echo "可用命令: cleanup, health, reset, rotate-logs, monitor"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
