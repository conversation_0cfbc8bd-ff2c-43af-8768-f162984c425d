#!/bin/bash

# New-API 更新管理脚本
# 负责拉取最新镜像、更新服务等功能

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
PROJECT_NAME="new-api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 检查Docker Compose文件
check_compose_file() {
    if [ ! -f "${COMPOSE_FILE}" ]; then
        log_error "Docker Compose 文件不存在: ${COMPOSE_FILE}"
        exit 1
    fi
}

# 获取当前镜像信息
get_current_image_info() {
    local service=$1
    local image_id=$(docker images --format "{{.ID}}" calciumion/new-api:latest | head -1)
    local created=$(docker images --format "{{.CreatedAt}}" calciumion/new-api:latest | head -1)
    
    if [ -n "$image_id" ]; then
        echo -e "${GREEN}当前镜像 ID:${NC} $image_id"
        echo -e "${GREEN}创建时间:${NC} $created"
    else
        echo -e "${YELLOW}未找到本地镜像${NC}"
    fi
}

# 拉取最新镜像
pull_images() {
    log_info "拉取最新镜像..."
    
    echo -e "\n${BLUE}=== 拉取前镜像信息 ===${NC}"
    get_current_image_info
    
    # 拉取所有服务的镜像
    local images=("calciumion/new-api:latest" "redis:latest" "mysql:8.2")
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        if docker pull "$image"; then
            log_info "✓ 成功拉取: $image"
        else
            log_error "✗ 拉取失败: $image"
            return 1
        fi
    done
    
    echo -e "\n${BLUE}=== 拉取后镜像信息 ===${NC}"
    get_current_image_info
    
    log_info "所有镜像拉取完成"
}

# 检查是否有更新
check_for_updates() {
    log_info "检查是否有可用更新..."
    
    # 获取本地镜像的摘要
    local local_digest=$(docker images --digests calciumion/new-api:latest --format "{{.Digest}}" | head -1)
    
    # 获取远程镜像的摘要（不拉取镜像）
    local remote_digest=$(docker manifest inspect calciumion/new-api:latest 2>/dev/null | grep -o '"digest":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    if [ "$local_digest" != "$remote_digest" ]; then
        log_info "发现新版本可用"
        return 0
    else
        log_info "当前已是最新版本"
        return 1
    fi
}

# 备份当前版本信息
backup_version_info() {
    local backup_file="${SCRIPT_DIR}/backup/version_info_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "备份当前版本信息到: $backup_file"
    
    {
        echo "=== 更新前版本信息 ==="
        echo "时间: $(date)"
        echo ""
        echo "=== Docker 镜像信息 ==="
        docker images calciumion/new-api:latest
        echo ""
        echo "=== 容器状态 ==="
        docker-compose -f "${COMPOSE_FILE}" ps
        echo ""
        echo "=== 容器详细信息 ==="
        docker ps --filter "name=new-api" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
    } > "$backup_file"
    
    log_info "版本信息已备份"
}

# 更新服务
update_service() {
    local force_update=${1:-false}
    
    log_info "开始更新 ${PROJECT_NAME} 服务..."
    
    # 检查是否有更新（除非强制更新）
    if [ "$force_update" != "true" ]; then
        if ! check_for_updates; then
            log_info "无需更新，退出"
            return 0
        fi
    fi
    
    # 备份版本信息
    backup_version_info
    
    # 创建数据库备份
    log_info "更新前创建数据库备份..."
    if [ -f "${SCRIPT_DIR}/scripts/backup.sh" ]; then
        bash "${SCRIPT_DIR}/scripts/backup.sh" backup --auto
    fi
    
    # 拉取最新镜像
    if ! pull_images; then
        log_error "镜像拉取失败，更新中止"
        exit 1
    fi
    
    # 停止服务
    log_info "停止当前服务..."
    docker-compose -f "${COMPOSE_FILE}" down
    
    # 启动服务
    log_info "启动更新后的服务..."
    docker-compose -f "${COMPOSE_FILE}" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 健康检查
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3000/api/status | grep -q '"success":true'; then
            log_info "✓ 服务更新成功并正常运行"
            break
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "服务启动超时，请检查日志"
        docker-compose -f "${COMPOSE_FILE}" logs --tail=50
        exit 1
    fi
    
    # 显示更新后的信息
    show_update_summary
}

# 显示更新摘要
show_update_summary() {
    echo -e "\n${BLUE}=== 更新完成摘要 ===${NC}"
    echo -e "${GREEN}更新时间:${NC} $(date)"
    echo -e "${GREEN}服务状态:${NC} 正常运行"
    echo -e "${GREEN}Web 界面:${NC} http://localhost:3000"
    
    echo -e "\n${GREEN}当前镜像信息:${NC}"
    get_current_image_info
    
    echo -e "\n${GREEN}容器状态:${NC}"
    docker-compose -f "${COMPOSE_FILE}" ps
}

# 回滚到之前版本
rollback_service() {
    log_warn "回滚功能需要手动实现"
    log_info "请检查备份目录中的版本信息文件，手动恢复到指定版本"
    
    echo -e "\n${YELLOW}回滚步骤:${NC}"
    echo "1. 停止当前服务: docker-compose down"
    echo "2. 拉取指定版本镜像: docker pull calciumion/new-api:<version>"
    echo "3. 修改 docker-compose.yml 中的镜像版本"
    echo "4. 启动服务: docker-compose up -d"
    echo "5. 如需要，恢复数据库备份"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理未使用的镜像..."
    
    # 显示当前镜像
    echo -e "\n${BLUE}=== 当前镜像 ===${NC}"
    docker images calciumion/new-api
    
    # 清理悬空镜像
    log_info "清理悬空镜像..."
    docker image prune -f
    
    log_info "镜像清理完成"
}

# 主函数
main() {
    check_compose_file
    
    local command=${1:-"update"}
    
    case $command in
        update)
            update_service false
            ;;
        force-update)
            update_service true
            ;;
        pull)
            pull_images
            ;;
        check)
            check_for_updates
            ;;
        rollback)
            rollback_service
            ;;
        cleanup)
            cleanup_old_images
            ;;
        info)
            get_current_image_info
            ;;
        *)
            log_error "未知命令: $command"
            echo "可用命令: update, force-update, pull, check, rollback, cleanup, info"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
