#!/bin/bash

# New-API 备份管理脚本
# 负责数据库备份、恢复、自动备份等功能

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
BACKUP_DIR="${SCRIPT_DIR}/backup"
PROJECT_NAME="new-api"

# 数据库配置（从docker-compose.yml中提取）
DB_CONTAINER="mysql"
DB_NAME="new-api"
DB_USER="root"
DB_PASSWORD="123456"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 检查数据库容器是否运行
check_db_container() {
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        log_error "数据库容器 $DB_CONTAINER 未运行"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="${BACKUP_DIR}/${backup_timestamp}"
    
    mkdir -p "$backup_path"
    echo "$backup_path"
}

# 备份数据库
backup_database() {
    local auto_mode=${1:-false}
    local backup_path
    
    if [ "$auto_mode" = "--auto" ]; then
        backup_path="${BACKUP_DIR}"
        local backup_file="${backup_path}/database_$(date +%Y%m%d_%H%M%S).sql"
    else
        backup_path=$(create_backup_dir)
        local backup_file="${backup_path}/database_backup.sql"
    fi
    
    log_info "开始备份数据库..."
    log_info "备份路径: $backup_file"
    
    # 检查数据库容器
    check_db_container
    
    # 执行数据库备份
    if docker exec "$DB_CONTAINER" mysqldump -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_file"; then
        log_info "✓ 数据库备份成功"
        
        # 创建最新备份的软链接
        ln -sf "$backup_file" "${BACKUP_DIR}/latest_backup.sql"
        
        # 如果不是自动模式，备份其他相关文件
        if [ "$auto_mode" != "--auto" ]; then
            backup_additional_files "$backup_path"
        fi
        
        # 显示备份信息
        show_backup_info "$backup_file"
        
    else
        log_error "✗ 数据库备份失败"
        exit 1
    fi
}

# 备份其他相关文件
backup_additional_files() {
    local backup_path=$1
    
    log_info "备份其他相关文件..."
    
    # 备份数据目录
    if [ -d "${SCRIPT_DIR}/data" ]; then
        log_info "备份数据目录..."
        cp -r "${SCRIPT_DIR}/data" "${backup_path}/new-api_data"
    fi
    
    # 备份配置文件
    if [ -f "${SCRIPT_DIR}/docker-compose.yml" ]; then
        log_info "备份配置文件..."
        cp "${SCRIPT_DIR}/docker-compose.yml" "${backup_path}/"
    fi
    
    # 备份环境信息
    {
        echo "=== 备份时间 ==="
        date
        echo ""
        echo "=== Docker 镜像信息 ==="
        docker images calciumion/new-api:latest
        echo ""
        echo "=== 容器状态 ==="
        docker-compose -f "${COMPOSE_FILE}" ps
        echo ""
        echo "=== Git 信息 ==="
        if [ -d "${SCRIPT_DIR}/.git" ]; then
            git log --oneline -10
        else
            echo "非 Git 仓库"
        fi
    } > "${backup_path}/backup_info.txt"
    
    log_info "✓ 其他文件备份完成"
}

# 显示备份信息
show_backup_info() {
    local backup_file=$1
    local file_size=$(du -h "$backup_file" | cut -f1)
    
    echo -e "\n${BLUE}=== 备份信息 ===${NC}"
    echo -e "${GREEN}备份文件:${NC} $backup_file"
    echo -e "${GREEN}文件大小:${NC} $file_size"
    echo -e "${GREEN}备份时间:${NC} $(date)"
    
    # 验证备份文件
    if [ -s "$backup_file" ]; then
        echo -e "${GREEN}备份状态:${NC} ✓ 成功"
    else
        echo -e "${RED}备份状态:${NC} ✗ 失败（文件为空）"
    fi
}

# 列出所有备份
list_backups() {
    echo -e "${BLUE}=== 可用备份列表 ===${NC}"
    
    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        log_warn "未找到任何备份文件"
        return 0
    fi
    
    echo -e "\n${GREEN}数据库备份文件:${NC}"
    find "$BACKUP_DIR" -name "*.sql" -type f | sort -r | while read -r backup_file; do
        local file_size=$(du -h "$backup_file" | cut -f1)
        local file_date=$(stat -c %y "$backup_file" | cut -d' ' -f1,2 | cut -d'.' -f1)
        echo "  $(basename "$backup_file") - $file_size - $file_date"
    done
    
    echo -e "\n${GREEN}完整备份目录:${NC}"
    find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" | sort -r | while read -r backup_dir; do
        local dir_size=$(du -sh "$backup_dir" | cut -f1)
        echo "  $(basename "$backup_dir") - $dir_size"
    done
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        echo "用法: $0 restore <backup_file>"
        echo "或者: $0 restore latest  # 恢复最新备份"
        list_backups
        exit 1
    fi
    
    # 处理特殊关键字
    if [ "$backup_file" = "latest" ]; then
        backup_file="${BACKUP_DIR}/latest_backup.sql"
    elif [ ! -f "$backup_file" ]; then
        # 尝试在备份目录中查找
        local full_path="${BACKUP_DIR}/$backup_file"
        if [ -f "$full_path" ]; then
            backup_file="$full_path"
        else
            log_error "备份文件不存在: $backup_file"
            exit 1
        fi
    fi
    
    log_warn "即将恢复数据库，这将覆盖当前所有数据！"
    echo -e "${YELLOW}备份文件: $backup_file${NC}"
    
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 检查数据库容器
    check_db_container
    
    # 创建恢复前备份
    log_info "创建恢复前备份..."
    backup_database --auto
    
    # 执行恢复
    log_info "开始恢复数据库..."
    if docker exec -i "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_file"; then
        log_info "✓ 数据库恢复成功"
        
        # 重启服务以确保数据一致性
        log_info "重启服务以确保数据一致性..."
        docker-compose -f "${COMPOSE_FILE}" restart new-api
        
        log_info "数据库恢复完成"
    else
        log_error "✗ 数据库恢复失败"
        exit 1
    fi
}

# 设置自动备份
setup_auto_backup() {
    local cron_schedule=${1:-"0 2 * * *"}  # 默认每天凌晨2点
    local script_path="${SCRIPT_DIR}/manage-new-api.sh"
    
    log_info "设置自动备份..."
    log_info "备份计划: $cron_schedule"
    
    # 创建cron任务
    local cron_job="$cron_schedule cd $SCRIPT_DIR && $script_path backup --auto >> ${SCRIPT_DIR}/logs/auto-backup.log 2>&1"
    
    # 检查是否已存在相同的cron任务
    if crontab -l 2>/dev/null | grep -q "$script_path backup"; then
        log_warn "自动备份任务已存在"
        crontab -l | grep "$script_path backup"
        return 0
    fi
    
    # 添加cron任务
    (crontab -l 2>/dev/null; echo "$cron_job") | crontab -
    
    log_info "✓ 自动备份已设置"
    echo -e "${GREEN}备份计划:${NC} $cron_schedule"
    echo -e "${GREEN}日志文件:${NC} ${SCRIPT_DIR}/logs/auto-backup.log"
    
    # 显示当前cron任务
    echo -e "\n${GREEN}当前cron任务:${NC}"
    crontab -l | grep "$script_path" || echo "无相关任务"
}

# 清理旧备份
cleanup_old_backups() {
    local keep_days=${1:-30}  # 默认保留30天
    
    log_info "清理 $keep_days 天前的备份..."
    
    # 清理旧的SQL备份文件
    find "$BACKUP_DIR" -name "database_*.sql" -type f -mtime +$keep_days -delete
    
    # 清理旧的备份目录
    find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" -mtime +$keep_days -exec rm -rf {} \;
    
    log_info "✓ 旧备份清理完成"
}

# 主函数
main() {
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    local command=${1:-"backup"}
    
    case $command in
        backup)
            shift
            backup_database "$@"
            ;;
        restore)
            shift
            restore_database "$@"
            ;;
        list)
            list_backups
            ;;
        auto-backup)
            shift
            setup_auto_backup "$@"
            ;;
        cleanup)
            shift
            cleanup_old_backups "$@"
            ;;
        *)
            log_error "未知命令: $command"
            echo "可用命令: backup, restore, list, auto-backup, cleanup"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
