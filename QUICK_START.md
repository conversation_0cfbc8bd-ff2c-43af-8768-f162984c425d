# New-API 管理脚本快速开始指南

## 🚀 一键安装

```bash
# 1. 运行安装脚本
./scripts/install.sh

# 2. 启动服务
./manage-new-api.sh start

# 3. 访问Web界面
# 打开浏览器访问: http://localhost:3000
```

## 📋 常用命令

### 服务管理
```bash
./manage-new-api.sh start      # 启动服务
./manage-new-api.sh stop       # 停止服务
./manage-new-api.sh restart    # 重启服务
./manage-new-api.sh status     # 查看状态
./manage-new-api.sh logs       # 查看日志
```

### 备份管理
```bash
./manage-new-api.sh backup         # 创建备份
./manage-new-api.sh list-backups   # 列出备份
./manage-new-api.sh restore latest # 恢复最新备份
./manage-new-api.sh auto-backup    # 设置自动备份
```

### 更新管理
```bash
./manage-new-api.sh update     # 更新到最新版本
./manage-new-api.sh pull       # 仅拉取最新镜像
```

### 维护管理
```bash
./manage-new-api.sh health     # 健康检查
./manage-new-api.sh cleanup    # 清理Docker资源
```

## 🔧 配置文件

### 主要配置文件
- `docker-compose.yml` - Docker服务配置
- `.env` - 环境变量配置
- `scripts/logrotate.conf` - 日志轮转配置

### 环境变量说明
```bash
# 数据库配置
DB_PASSWORD=123456          # 数据库密码
DB_NAME=new-api            # 数据库名称
DB_USER=root               # 数据库用户

# 备份配置
BACKUP_RETENTION_DAYS=30   # 备份保留天数
AUTO_BACKUP_SCHEDULE="0 2 * * *"  # 自动备份时间（每天凌晨2点）
```

## 📁 目录结构

```
new-api/
├── manage-new-api.sh          # 主管理脚本
├── docker-compose.yml         # Docker配置
├── .env                       # 环境配置
├── scripts/                   # 子脚本目录
│   ├── service.sh            # 服务管理
│   ├── backup.sh             # 备份管理
│   ├── update.sh             # 更新管理
│   ├── maintenance.sh        # 维护管理
│   └── install.sh            # 安装脚本
├── backup/                    # 备份文件
├── logs/                      # 日志文件
└── data/                      # 应用数据
```

## 🔍 故障排除

### 常见问题

1. **权限问题**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   ./manage-new-api.sh logs
   
   # 健康检查
   ./manage-new-api.sh health
   ```

4. **数据库连接问题**
   ```bash
   # 检查数据库容器状态
   docker ps | grep mysql
   
   # 查看数据库日志
   ./manage-new-api.sh logs mysql
   ```

### 获取帮助

```bash
# 查看完整帮助
./manage-new-api.sh help

# 查看版本信息
./manage-new-api.sh version
```

## 🛡️ 安全建议

1. **修改默认密码**: 编辑 `.env` 文件中的 `DB_PASSWORD`
2. **定期备份**: 启用自动备份功能
3. **监控日志**: 定期检查日志文件
4. **更新系统**: 定期运行更新命令

## 📞 支持

如果遇到问题，请：
1. 查看 `MANAGEMENT_README.md` 获取详细文档
2. 运行 `./manage-new-api.sh health` 进行健康检查
3. 查看日志文件排查问题
4. 在项目仓库提交Issue

---

**祝您使用愉快！** 🎉
