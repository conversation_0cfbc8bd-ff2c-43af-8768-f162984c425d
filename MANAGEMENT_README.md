# New-API 综合管理脚本

这是一个用于管理 New-API 项目的综合脚本系统，提供了启动、停止、备份、更新、维护等全方位的管理功能。

## 🚀 快速开始

### 1. 安装和初始化

```bash
# 运行安装脚本
bash scripts/install.sh

# 如果需要创建systemd服务（可选）
bash scripts/install.sh --systemd
```

### 2. 基本使用

```bash
# 启动服务
./manage-new-api.sh start

# 查看服务状态
./manage-new-api.sh status

# 查看帮助
./manage-new-api.sh help
```

## 📋 功能特性

### 🔧 服务管理
- **启动服务**: 启动 New-API 及相关服务（MySQL、Redis）
- **停止服务**: 安全停止所有服务
- **重启服务**: 重启服务并进行健康检查
- **状态查看**: 实时查看服务运行状态和资源使用情况
- **日志查看**: 查看服务日志，支持实时跟踪

### 🔄 更新管理
- **自动更新**: 检查并拉取最新镜像，自动重启服务
- **镜像拉取**: 仅拉取最新镜像而不重启
- **版本检查**: 检查是否有可用更新
- **版本备份**: 更新前自动备份当前版本信息

### 💾 备份管理
- **数据库备份**: 完整的MySQL数据库备份
- **自动备份**: 支持cron定时自动备份
- **备份恢复**: 从备份文件恢复数据库
- **备份列表**: 查看所有可用备份
- **备份清理**: 自动清理过期备份文件

### 🛠️ 维护管理
- **健康检查**: 全面的系统健康状态检查
- **资源清理**: 清理未使用的Docker资源
- **性能监控**: 实时监控系统和容器性能
- **日志轮转**: 自动轮转和压缩日志文件
- **服务重置**: 完全重置服务（危险操作）

## 📖 详细使用说明

### 服务管理命令

```bash
# 启动所有服务
./manage-new-api.sh start

# 停止所有服务
./manage-new-api.sh stop

# 重启所有服务
./manage-new-api.sh restart

# 查看服务状态
./manage-new-api.sh status

# 查看服务日志（所有服务）
./manage-new-api.sh logs

# 查看特定服务日志
./manage-new-api.sh logs new-api

# 实时跟踪日志
./manage-new-api.sh logs new-api 50
```

### 更新管理命令

```bash
# 检查并更新到最新版本
./manage-new-api.sh update

# 仅拉取最新镜像
./manage-new-api.sh pull

# 检查是否有可用更新
./manage-new-api.sh check-update
```

### 备份管理命令

```bash
# 手动备份数据库
./manage-new-api.sh backup

# 恢复最新备份
./manage-new-api.sh restore latest

# 恢复指定备份文件
./manage-new-api.sh restore backup/database_20240101_120000.sql

# 列出所有备份
./manage-new-api.sh list-backups

# 设置自动备份（每天凌晨2点）
./manage-new-api.sh auto-backup

# 设置自定义备份时间
./manage-new-api.sh auto-backup "0 3 * * *"
```

### 维护管理命令

```bash
# 系统健康检查
./manage-new-api.sh health

# 清理Docker资源
./manage-new-api.sh cleanup

# 性能监控
./manage-new-api.sh monitor

# 重置服务（危险操作）
./manage-new-api.sh reset
```

## 📁 目录结构

```
new-api/
├── manage-new-api.sh          # 主管理脚本
├── docker-compose.yml         # Docker Compose 配置
├── .env                       # 环境配置文件
├── scripts/                   # 子脚本目录
│   ├── service.sh            # 服务管理脚本
│   ├── update.sh             # 更新管理脚本
│   ├── backup.sh             # 备份管理脚本
│   ├── maintenance.sh        # 维护管理脚本
│   ├── install.sh            # 安装初始化脚本
│   └── logrotate.conf        # 日志轮转配置
├── backup/                    # 备份文件目录
│   ├── latest_backup.sql     # 最新备份软链接
│   └── YYYYMMDD_HHMMSS/      # 按时间戳命名的备份目录
├── logs/                      # 日志文件目录
│   ├── manage.log            # 管理脚本日志
│   └── auto-backup.log       # 自动备份日志
└── data/                      # 应用数据目录
```

## ⚙️ 配置说明

### 环境配置文件 (.env)

```bash
# 数据库配置
DB_PASSWORD=123456
DB_NAME=new-api
DB_USER=root

# 时区设置
TZ=Asia/Shanghai

# 备份配置
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=false
AUTO_BACKUP_SCHEDULE="0 2 * * *"

# 监控配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
```

### Docker Compose 配置

主要配置项在 `docker-compose.yml` 中：
- 端口映射：默认3000端口
- 数据卷挂载：./data 和 ./logs
- 环境变量：数据库连接、Redis连接等
- 健康检查：API状态检查

## 🔒 安全注意事项

1. **数据库密码**: 请修改默认的数据库密码
2. **备份安全**: 备份文件包含敏感数据，请妥善保管
3. **权限管理**: 确保脚本文件有适当的执行权限
4. **网络安全**: 生产环境请配置防火墙和SSL证书

## 🚨 故障排除

### 常见问题

1. **Docker权限问题**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   # 或使用其他端口
   ```

3. **数据库连接失败**
   - 检查MySQL容器是否正常运行
   - 验证数据库密码配置
   - 查看数据库日志

4. **备份恢复失败**
   - 确保备份文件完整性
   - 检查数据库容器状态
   - 验证数据库权限

### 日志查看

```bash
# 查看管理脚本日志
tail -f logs/manage.log

# 查看自动备份日志
tail -f logs/auto-backup.log

# 查看容器日志
./manage-new-api.sh logs
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个管理脚本系统。

## 📄 许可证

本项目遵循与New-API主项目相同的许可证。
